import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../contexts/AuthContext';
import { useFinancialData } from '../hooks/useFinancialData';

const DashboardScreen: React.FC = () => {
  const { user } = useAuth();
  const { loading, getDashboardSummary, refetch } = useFinancialData();
  const summary = getDashboardSummary();

  const onRefresh = React.useCallback(async () => {
    await refetch();
  }, [refetch]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const SummaryCard: React.FC<{
    title: string;
    amount: number;
    icon: keyof typeof Ionicons.glyphMap;
    color: string;
  }> = ({ title, amount, icon, color }) => (
    <View style={[styles.summaryCard, { borderLeftColor: color }]}>
      <View style={styles.summaryCardHeader}>
        <Ionicons name={icon} size={24} color={color} />
        <Text style={styles.summaryCardTitle}>{title}</Text>
      </View>
      <Text style={[styles.summaryCardAmount, { color }]}>
        {formatCurrency(amount)}
      </Text>
    </View>
  );

  return (
    <ScrollView
      style={styles.container}
      refreshControl={
        <RefreshControl refreshing={loading} onRefresh={onRefresh} />
      }
    >
      <View style={styles.header}>
        <Text style={styles.greeting}>
          Welcome back, {user?.full_name || 'User'}!
        </Text>
        <Text style={styles.date}>
          {new Date().toLocaleDateString('en-US', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric',
          })}
        </Text>
      </View>

      <View style={styles.netWorthCard}>
        <Text style={styles.netWorthLabel}>Net Worth</Text>
        <Text style={styles.netWorthAmount}>
          {formatCurrency(summary.netWorth)}
        </Text>
      </View>

      <View style={styles.summaryGrid}>
        <SummaryCard
          title="Total Balance"
          amount={summary.totalBalance}
          icon="wallet"
          color="#4CAF50"
        />
        <SummaryCard
          title="Total Assets"
          amount={summary.totalAssets}
          icon="business"
          color="#2196F3"
        />
        <SummaryCard
          title="Active Loans"
          amount={summary.totalLoans}
          icon="cash"
          color="#FF9800"
        />
        <SummaryCard
          title="Pending Debts"
          amount={summary.totalDebts}
          icon="people"
          color="#F44336"
        />
      </View>

      <View style={styles.monthlyOverview}>
        <Text style={styles.sectionTitle}>This Month</Text>
        <View style={styles.monthlyRow}>
          <View style={styles.monthlyItem}>
            <Ionicons name="trending-up" size={20} color="#4CAF50" />
            <Text style={styles.monthlyLabel}>Income</Text>
            <Text style={[styles.monthlyAmount, { color: '#4CAF50' }]}>
              {formatCurrency(summary.monthlyIncome)}
            </Text>
          </View>
          <View style={styles.monthlyItem}>
            <Ionicons name="trending-down" size={20} color="#F44336" />
            <Text style={styles.monthlyLabel}>Expenses</Text>
            <Text style={[styles.monthlyAmount, { color: '#F44336' }]}>
              {formatCurrency(summary.monthlyExpenses)}
            </Text>
          </View>
        </View>
      </View>

      <View style={styles.recentTransactions}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Recent Transactions</Text>
          <TouchableOpacity>
            <Text style={styles.seeAllText}>See All</Text>
          </TouchableOpacity>
        </View>
        
        {summary.recentTransactions.length === 0 ? (
          <View style={styles.emptyState}>
            <Ionicons name="receipt-outline" size={48} color="#ccc" />
            <Text style={styles.emptyStateText}>No recent transactions</Text>
            <Text style={styles.emptyStateSubtext}>
              Start by adding your first transaction
            </Text>
          </View>
        ) : (
          summary.recentTransactions.map((transaction) => (
            <View key={transaction.id} style={styles.transactionItem}>
              <View style={styles.transactionInfo}>
                <Text style={styles.transactionDescription}>
                  {transaction.description || transaction.category}
                </Text>
                <Text style={styles.transactionDate}>
                  {new Date(transaction.date).toLocaleDateString()}
                </Text>
              </View>
              <Text
                style={[
                  styles.transactionAmount,
                  {
                    color: transaction.type === 'income' ? '#4CAF50' : '#F44336',
                  },
                ]}
              >
                {transaction.type === 'income' ? '+' : '-'}
                {formatCurrency(Math.abs(transaction.amount))}
              </Text>
            </View>
          ))
        )}
      </View>

      <View style={styles.quickActions}>
        <Text style={styles.sectionTitle}>Quick Actions</Text>
        <View style={styles.actionGrid}>
          <TouchableOpacity style={styles.actionButton}>
            <Ionicons name="add-circle" size={24} color="#007AFF" />
            <Text style={styles.actionText}>Add Transaction</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.actionButton}>
            <Ionicons name="card" size={24} color="#007AFF" />
            <Text style={styles.actionText}>Add Account</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.actionButton}>
            <Ionicons name="cash" size={24} color="#007AFF" />
            <Text style={styles.actionText}>Add Loan</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.actionButton}>
            <Ionicons name="business" size={24} color="#007AFF" />
            <Text style={styles.actionText}>Add Asset</Text>
          </TouchableOpacity>
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    padding: 20,
    backgroundColor: 'white',
  },
  greeting: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
  },
  date: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  netWorthCard: {
    backgroundColor: '#007AFF',
    margin: 20,
    padding: 20,
    borderRadius: 12,
    alignItems: 'center',
  },
  netWorthLabel: {
    color: 'white',
    fontSize: 16,
    opacity: 0.9,
  },
  netWorthAmount: {
    color: 'white',
    fontSize: 32,
    fontWeight: 'bold',
    marginTop: 4,
  },
  summaryGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: 20,
    gap: 10,
  },
  summaryCard: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 16,
    flex: 1,
    minWidth: '45%',
    borderLeftWidth: 4,
  },
  summaryCardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  summaryCardTitle: {
    fontSize: 14,
    color: '#666',
    marginLeft: 8,
  },
  summaryCardAmount: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  monthlyOverview: {
    backgroundColor: 'white',
    margin: 20,
    padding: 20,
    borderRadius: 8,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
  },
  monthlyRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  monthlyItem: {
    alignItems: 'center',
  },
  monthlyLabel: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  monthlyAmount: {
    fontSize: 16,
    fontWeight: 'bold',
    marginTop: 4,
  },
  recentTransactions: {
    backgroundColor: 'white',
    margin: 20,
    padding: 20,
    borderRadius: 8,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  seeAllText: {
    color: '#007AFF',
    fontSize: 14,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyStateText: {
    fontSize: 16,
    color: '#666',
    marginTop: 12,
  },
  emptyStateSubtext: {
    fontSize: 14,
    color: '#999',
    marginTop: 4,
  },
  transactionItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  transactionInfo: {
    flex: 1,
  },
  transactionDescription: {
    fontSize: 16,
    color: '#333',
  },
  transactionDate: {
    fontSize: 12,
    color: '#666',
    marginTop: 2,
  },
  transactionAmount: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  quickActions: {
    backgroundColor: 'white',
    margin: 20,
    padding: 20,
    borderRadius: 8,
    marginBottom: 40,
  },
  actionGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  actionButton: {
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    flex: 1,
    minWidth: '45%',
  },
  actionText: {
    fontSize: 12,
    color: '#007AFF',
    marginTop: 4,
    textAlign: 'center',
  },
});

export default DashboardScreen;
