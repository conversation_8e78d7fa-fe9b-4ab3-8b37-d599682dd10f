import { useState, useEffect } from 'react';
import { supabase } from '../../lib/supabase';
import { useAuth } from '../contexts/AuthContext';
import { 
  Account, 
  Transaction, 
  Loan, 
  Asset, 
  PortfolioItem, 
  Debt, 
  DashboardSummary 
} from '../types';

export const useFinancialData = () => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [accounts, setAccounts] = useState<Account[]>([]);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [loans, setLoans] = useState<Loan[]>([]);
  const [assets, setAssets] = useState<Asset[]>([]);
  const [portfolio, setPortfolio] = useState<PortfolioItem[]>([]);
  const [debts, setDebts] = useState<Debt[]>([]);

  const fetchAccounts = async () => {
    if (!user) return;
    
    const { data, error } = await supabase
      .from('accounts')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching accounts:', error);
    } else {
      setAccounts(data || []);
    }
  };

  const fetchTransactions = async () => {
    if (!user) return;
    
    const { data, error } = await supabase
      .from('transactions')
      .select(`
        *,
        accounts(name),
        categories(name, color, icon)
      `)
      .eq('user_id', user.id)
      .order('date', { ascending: false })
      .limit(50);

    if (error) {
      console.error('Error fetching transactions:', error);
    } else {
      setTransactions(data || []);
    }
  };

  const fetchLoans = async () => {
    if (!user) return;
    
    const { data, error } = await supabase
      .from('loans')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching loans:', error);
    } else {
      setLoans(data || []);
    }
  };

  const fetchAssets = async () => {
    if (!user) return;
    
    const { data, error } = await supabase
      .from('assets')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching assets:', error);
    } else {
      setAssets(data || []);
    }
  };

  const fetchPortfolio = async () => {
    if (!user) return;
    
    const { data, error } = await supabase
      .from('portfolio')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching portfolio:', error);
    } else {
      setPortfolio(data || []);
    }
  };

  const fetchDebts = async () => {
    if (!user) return;
    
    const { data, error } = await supabase
      .from('debts')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching debts:', error);
    } else {
      setDebts(data || []);
    }
  };

  const fetchAllData = async () => {
    setLoading(true);
    await Promise.all([
      fetchAccounts(),
      fetchTransactions(),
      fetchLoans(),
      fetchAssets(),
      fetchPortfolio(),
      fetchDebts(),
    ]);
    setLoading(false);
  };

  useEffect(() => {
    if (user) {
      fetchAllData();
    }
  }, [user]);

  const getDashboardSummary = (): DashboardSummary => {
    const totalBalance = accounts.reduce((sum, account) => sum + Number(account.balance), 0);
    const totalAssets = assets.reduce((sum, asset) => sum + Number(asset.current_value), 0);
    const totalPortfolioValue = portfolio.reduce(
      (sum, item) => sum + (Number(item.quantity) * Number(item.current_price)), 
      0
    );
    
    const activeLoans = loans.filter(loan => loan.status === 'active');
    const totalLoansGiven = activeLoans
      .filter(loan => loan.type === 'given')
      .reduce((sum, loan) => sum + Number(loan.amount), 0);
    const totalLoansTaken = activeLoans
      .filter(loan => loan.type === 'taken')
      .reduce((sum, loan) => sum + Number(loan.amount), 0);
    
    const totalDebts = debts
      .filter(debt => debt.status !== 'paid')
      .reduce((sum, debt) => sum + (Number(debt.amount) - Number(debt.amount_paid)), 0);

    const netWorth = totalBalance + totalAssets + totalPortfolioValue + totalLoansGiven - totalLoansTaken;

    // Calculate monthly income and expenses
    const currentMonth = new Date().getMonth();
    const currentYear = new Date().getFullYear();
    
    const monthlyTransactions = transactions.filter(transaction => {
      const transactionDate = new Date(transaction.date);
      return transactionDate.getMonth() === currentMonth && 
             transactionDate.getFullYear() === currentYear;
    });

    const monthlyIncome = monthlyTransactions
      .filter(t => t.type === 'income')
      .reduce((sum, t) => sum + Number(t.amount), 0);
    
    const monthlyExpenses = monthlyTransactions
      .filter(t => t.type === 'expense')
      .reduce((sum, t) => sum + Number(t.amount), 0);

    const recentTransactions = transactions.slice(0, 5);

    return {
      totalBalance,
      totalAssets: totalAssets + totalPortfolioValue,
      totalLoans: totalLoansGiven - totalLoansTaken,
      totalDebts,
      netWorth,
      monthlyIncome,
      monthlyExpenses,
      recentTransactions,
    };
  };

  return {
    loading,
    accounts,
    transactions,
    loans,
    assets,
    portfolio,
    debts,
    getDashboardSummary,
    refetch: fetchAllData,
    fetchAccounts,
    fetchTransactions,
    fetchLoans,
    fetchAssets,
    fetchPortfolio,
    fetchDebts,
  };
};
