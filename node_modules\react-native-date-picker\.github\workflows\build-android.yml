name: Build

on:
  workflow_call:
  workflow_dispatch:

env:
  APP_PATH: examples/Rn071/android/app/build/outputs/apk/release/app-release.apk
  PROJECT_NAME: Rn071
  EXAMPLE_SRC: ./examples/Rn071/src/**

jobs:
  build-android:
    name: Android
    runs-on: ubuntu-latest
    outputs:
      app: app/build/outputs/apk/release
    steps:
      - uses: actions/checkout@v4
        with:
          ref: ${{ github.event.pull_request.head.sha }}

      - uses: actions/cache@v4
        id: cache-android
        with:
          path: ${{ env.APP_PATH }}
          key: ${{ runner.os }}-${{ hashFiles('src/**', 'android/**', env.EXAMPLE_SRC, env.APP_PATH ) }}

      - uses: actions/setup-node@v4
        if: steps.cache-android.outputs.cache-hit != 'true'
        with:
          node-version: 18
          cache: 'yarn'

      - name: Install npm dependencies (example project)
        if: steps.cache-android.outputs.cache-hit != 'true'
        working-directory: ./examples/${{ env.PROJECT_NAME }}/
        run: yarn install --frozen-lockfile

      - name: Install npm dependencies (root)
        if: steps.cache-android.outputs.cache-hit != 'true'
        working-directory: ./
        run: yarn install --frozen-lockfile

      - uses: actions/setup-java@v4
        if: steps.cache-android.outputs.cache-hit != 'true'
        with:
          java-version: 11
          distribution: 'temurin'

      - name: build
        if: steps.cache-android.outputs.cache-hit != 'true'
        working-directory: ./examples/${{ env.PROJECT_NAME }}/android
        run: ./gradlew assembleRelease
