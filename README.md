# Personal Expense Manager

A comprehensive cross-platform personal finance management application built with React Native, Expo, and Supabase.

## Features

### ✅ Completed Features
- **User Authentication**: Email/password authentication with <PERSON><PERSON><PERSON> Auth
- **Cross-Platform Support**: Runs on iOS, Android, and Web
- **Dashboard**: Financial overview with net worth, recent transactions, and quick actions
- **Account Management**: Track multiple bank accounts (checking, savings, credit, etc.)
- **Transaction Tracking**: Income and expense tracking with categorization
- **Loan Management**: Track loans given and taken with payment schedules
- **Asset Tracking**: Monitor valuable possessions and their appreciation/depreciation
- **Investment Portfolio**: Track stocks, crypto, bonds, and other investments
- **Debt Management**: Keep track of money owed by others with payment progress
- **Financial Reports**: Comprehensive reporting with insights and analytics
- **Settings**: User preferences, data management, and security options

### 🚧 In Development
- Transaction creation forms
- Account balance synchronization
- Real-time portfolio price updates
- Advanced reporting and charts
- Data export/import functionality
- Push notifications
- Biometric authentication

## Tech Stack

- **Frontend**: React Native with Expo
- **Backend**: Supabase (PostgreSQL database, Authentication, Real-time subscriptions)
- **Navigation**: React Navigation 6
- **UI Components**: React Native Elements, Expo Vector Icons
- **Charts**: React Native Chart Kit
- **State Management**: React Context API
- **TypeScript**: Full TypeScript support

## Database Schema

The app uses a comprehensive PostgreSQL schema with the following tables:
- `profiles` - User profile information
- `accounts` - Bank accounts and financial accounts
- `categories` - Transaction categories (income/expense)
- `transactions` - Financial transactions
- `loans` - Loans given and taken
- `assets` - Valuable possessions and investments
- `portfolio` - Investment holdings
- `debts` - Money owed by others

All tables include Row Level Security (RLS) policies for data protection.

## Setup Instructions

### Prerequisites
- Node.js (v16 or higher)
- npm or yarn
- Expo CLI (`npm install -g @expo/cli`)
- Supabase account

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd Personal_manager
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Configure Supabase**
   - Go to your Supabase project dashboard
   - Navigate to Settings > API
   - Copy your Project URL and anon public key
   - Update `lib/supabase.ts` with your credentials:
   ```typescript
   const supabaseUrl = 'YOUR_SUPABASE_PROJECT_URL';
   const supabaseAnonKey = 'YOUR_SUPABASE_ANON_KEY';
   ```

4. **Database Setup**
   The database schema has already been created in your Supabase project. It includes:
   - All necessary tables with proper relationships
   - Row Level Security (RLS) policies
   - Triggers for data integrity
   - Default categories for new users

### Running the App

**Web Development**
```bash
npm run web
```

**iOS Simulator** (macOS only)
```bash
npm run ios
```

**Android Emulator**
```bash
npm run android
```

**Expo Go** (Physical device)
```bash
npx expo start
```
Then scan the QR code with Expo Go app.

## Project Structure

```
src/
├── contexts/          # React contexts (Auth, etc.)
├── navigation/        # Navigation configuration
├── screens/          # App screens
├── types/            # TypeScript type definitions
└── components/       # Reusable components (to be added)

lib/
└── supabase.ts       # Supabase configuration

assets/               # Images and static assets
```

## Key Features Walkthrough

### Authentication
- Email/password sign up and sign in
- Automatic profile creation with default categories
- Secure session management

### Dashboard
- Net worth calculation
- Monthly income/expense overview
- Recent transactions
- Quick action buttons

### Account Management
- Multiple account types support
- Real-time balance tracking
- Account-specific transaction history

### Transaction System
- Income and expense tracking
- Category-based organization
- Search and filter functionality
- Automatic account balance updates

### Loan Tracking
- Separate tracking for loans given vs taken
- Interest rate calculations
- Due date management
- Status tracking (active, paid, overdue)

### Asset Management
- Multiple asset types (real estate, vehicles, etc.)
- Purchase price vs current value tracking
- Appreciation/depreciation calculations

### Investment Portfolio
- Stock, crypto, bond, and fund tracking
- Profit/loss calculations
- Portfolio performance metrics

### Debt Management
- Track money owed by others
- Payment progress tracking
- Contact information management

## Security Features

- Row Level Security (RLS) on all database tables
- User data isolation
- Secure authentication with Supabase Auth
- Automatic session management

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For support and questions, please open an issue in the repository.
