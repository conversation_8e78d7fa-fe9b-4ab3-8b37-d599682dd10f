import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
  RefreshControl,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useFinancialData } from '../hooks/useFinancialData';
import AddAssetModal from '../components/AddAssetModal';
import { Asset } from '../types';

const AssetsScreen: React.FC = () => {
  const { assets, loading, fetchAssets } = useFinancialData();
  const [showAddModal, setShowAddModal] = useState(false);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const getAssetIcon = (type: string) => {
    switch (type) {
      case 'real_estate':
        return 'home';
      case 'vehicle':
        return 'car';
      case 'investment':
        return 'trending-up';
      case 'jewelry':
        return 'diamond';
      case 'electronics':
        return 'phone-portrait';
      default:
        return 'cube';
    }
  };

  const getAssetColor = (type: string) => {
    switch (type) {
      case 'real_estate':
        return '#4CAF50';
      case 'vehicle':
        return '#2196F3';
      case 'investment':
        return '#9C27B0';
      case 'jewelry':
        return '#FF9800';
      case 'electronics':
        return '#607D8B';
      default:
        return '#666';
    }
  };

  const handleAddAsset = () => {
    setShowAddModal(true);
  };

  const handleAssetAdded = () => {
    fetchAssets();
  };

  const renderAsset = ({ item }: { item: Asset }) => {
    const valueChange = item.current_value - item.purchase_price;
    const valueChangePercent = (valueChange / item.purchase_price) * 100;

    return (
      <TouchableOpacity style={styles.assetCard}>
        <View style={styles.assetHeader}>
          <View style={styles.assetInfo}>
            <Ionicons
              name={getAssetIcon(item.type)}
              size={24}
              color={getAssetColor(item.type)}
            />
            <View style={styles.assetDetails}>
              <Text style={styles.assetName}>{item.name}</Text>
              <Text style={styles.assetType}>
                {item.type.replace('_', ' ').charAt(0).toUpperCase() + 
                 item.type.replace('_', ' ').slice(1)}
              </Text>
              <Text style={styles.purchaseDate}>
                Purchased: {new Date(item.purchase_date).toLocaleDateString()}
              </Text>
            </View>
          </View>
          <View style={styles.valueContainer}>
            <Text style={styles.currentValue}>
              {formatCurrency(item.current_value)}
            </Text>
            <Text
              style={[
                styles.valueChange,
                { color: valueChange >= 0 ? '#4CAF50' : '#F44336' },
              ]}
            >
              {valueChange >= 0 ? '+' : ''}
              {formatCurrency(valueChange)} ({valueChangePercent.toFixed(1)}%)
            </Text>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  const EmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons name="cube-outline" size={64} color="#ccc" />
      <Text style={styles.emptyStateTitle}>No Assets Yet</Text>
      <Text style={styles.emptyStateText}>
        Track your valuable possessions and investments
      </Text>
      <TouchableOpacity style={styles.addButton} onPress={handleAddAsset}>
        <Ionicons name="add" size={24} color="white" />
        <Text style={styles.addButtonText}>Add Asset</Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <View style={styles.container}>
      {assets.length === 0 ? (
        <EmptyState />
      ) : (
        <>
          <FlatList
            data={assets}
            renderItem={renderAsset}
            keyExtractor={(item) => item.id}
            contentContainerStyle={styles.listContainer}
            refreshControl={
              <RefreshControl refreshing={loading} onRefresh={fetchAssets} />
            }
          />
          <TouchableOpacity style={styles.fab} onPress={handleAddAsset}>
            <Ionicons name="add" size={24} color="white" />
          </TouchableOpacity>
        </>
      )}

      <AddAssetModal
        visible={showAddModal}
        onClose={() => setShowAddModal(false)}
        onAssetAdded={handleAssetAdded}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  listContainer: {
    padding: 16,
  },
  assetCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  assetHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  assetInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  assetDetails: {
    marginLeft: 12,
    flex: 1,
  },
  assetName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  assetType: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  purchaseDate: {
    fontSize: 12,
    color: '#999',
    marginTop: 2,
  },
  valueContainer: {
    alignItems: 'flex-end',
  },
  currentValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  valueChange: {
    fontSize: 12,
    marginTop: 2,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  emptyStateTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 16,
  },
  emptyStateText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginTop: 8,
    marginBottom: 32,
  },
  addButton: {
    backgroundColor: '#007AFF',
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  addButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  fab: {
    position: 'absolute',
    bottom: 20,
    right: 20,
    backgroundColor: '#007AFF',
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
});

export default AssetsScreen;
