<?xml version="1.0" encoding="utf-8"?>
<resources>
    <declare-styleable name="Theme">
        <!-- NumberPicker style. -->
        <attr name="numberPickerStyle" format="reference" />
    </declare-styleable>
    <declare-styleable name="NumberPicker">
        <!-- @hide Color for the solid color background if such for optimized rendering. -->
        <attr name="solidColor" format="color|reference" />
        <!-- @hide The divider for making the selection area. -->
        <attr name="selectionDivider" format="reference" />
        <!-- The height of the selection divider. -->
        <attr name="selectionDividerHeight" format="dimension" />
        <!-- @hide The distance between the two selection dividers. -->
        <attr name="selectionDividersDistance" format="dimension" />
        <!-- @hide The min height of the NumberPicker. -->
        <attr name="internalMinHeight" format="dimension" />
        <!-- @hide The max height of the NumberPicker. -->
        <attr name="internalMaxHeight" format="dimension" />
        <!-- @hide The min width of the NumberPicker. -->
        <attr name="internalMinWidth" format="dimension" />
        <!-- @hide The max width of the NumberPicker. -->
        <attr name="internalMaxWidth" format="dimension" />
        <!-- @hide The layout of the number picker. -->
        <attr name="internalLayout" />
        <!-- @hide The drawable for pressed virtual (increment/decrement) buttons. -->
        <attr name="virtualButtonPressedDrawable" format="reference"/>
        <!-- @hide If true then the selector wheel is hidden until the picker has focus. -->
        <attr name="hideWheelUntilFocused" format="boolean"/>
    </declare-styleable>

</resources>