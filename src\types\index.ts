// User types
export interface User {
  id: string;
  email: string;
  full_name?: string;
  avatar_url?: string;
  created_at: string;
  updated_at: string;
}

// Account types
export type AccountType = 'checking' | 'savings' | 'credit' | 'investment' | 'cash';

export interface Account {
  id: string;
  user_id: string;
  name: string;
  type: AccountType;
  balance: number;
  currency: string;
  created_at: string;
  updated_at: string;
}

// Transaction types
export type TransactionType = 'income' | 'expense' | 'transfer';

export interface Transaction {
  id: string;
  user_id: string;
  account_id: string;
  amount: number;
  type: TransactionType;
  category: string;
  description?: string;
  date: string;
  created_at: string;
  updated_at: string;
}

// Loan types
export type LoanType = 'given' | 'taken';
export type LoanStatus = 'active' | 'paid' | 'overdue';

export interface Loan {
  id: string;
  user_id: string;
  type: LoanType;
  amount: number;
  interest_rate?: number;
  borrower_name?: string; // For loans given
  lender_name?: string;   // For loans taken
  description?: string;
  due_date?: string;
  status: LoanStatus;
  created_at: string;
  updated_at: string;
}

// Asset types
export type AssetType = 'real_estate' | 'vehicle' | 'investment' | 'jewelry' | 'electronics' | 'other';

export interface Asset {
  id: string;
  user_id: string;
  name: string;
  type: AssetType;
  purchase_price: number;
  current_value: number;
  purchase_date: string;
  description?: string;
  created_at: string;
  updated_at: string;
}

// Portfolio types
export type PortfolioType = 'stock' | 'crypto' | 'bond' | 'mutual_fund' | 'etf';

export interface PortfolioItem {
  id: string;
  user_id: string;
  symbol: string;
  name: string;
  type: PortfolioType;
  quantity: number;
  purchase_price: number;
  current_price: number;
  purchase_date: string;
  created_at: string;
  updated_at: string;
}

// Debt types (money owed by others)
export type DebtStatus = 'pending' | 'partial' | 'paid';

export interface Debt {
  id: string;
  user_id: string;
  debtor_name: string;
  debtor_contact?: string;
  amount: number;
  amount_paid: number;
  description?: string;
  due_date?: string;
  status: DebtStatus;
  created_at: string;
  updated_at: string;
}

// Category types
export interface Category {
  id: string;
  user_id: string;
  name: string;
  type: 'income' | 'expense';
  color?: string;
  icon?: string;
  created_at: string;
  updated_at: string;
}

// Navigation types
export type RootStackParamList = {
  Auth: undefined;
  Main: undefined;
};

export type MainTabParamList = {
  Dashboard: undefined;
  Accounts: undefined;
  Transactions: undefined;
  Loans: undefined;
  Assets: undefined;
  Portfolio: undefined;
  Debts: undefined;
  Reports: undefined;
  Settings: undefined;
};

// Dashboard summary types
export interface DashboardSummary {
  totalBalance: number;
  totalAssets: number;
  totalLoans: number;
  totalDebts: number;
  netWorth: number;
  monthlyIncome: number;
  monthlyExpenses: number;
  recentTransactions: Transaction[];
}
