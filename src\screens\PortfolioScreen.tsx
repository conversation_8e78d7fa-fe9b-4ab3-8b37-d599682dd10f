import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { PortfolioItem } from '../types';

const PortfolioScreen: React.FC = () => {
  const [portfolio, setPortfolio] = useState<PortfolioItem[]>([]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const getPortfolioIcon = (type: string) => {
    switch (type) {
      case 'stock':
        return 'trending-up';
      case 'crypto':
        return 'logo-bitcoin';
      case 'bond':
        return 'document';
      case 'mutual_fund':
        return 'pie-chart';
      case 'etf':
        return 'bar-chart';
      default:
        return 'trending-up';
    }
  };

  const getPortfolioColor = (type: string) => {
    switch (type) {
      case 'stock':
        return '#4CAF50';
      case 'crypto':
        return '#FF9800';
      case 'bond':
        return '#2196F3';
      case 'mutual_fund':
        return '#9C27B0';
      case 'etf':
        return '#607D8B';
      default:
        return '#666';
    }
  };

  const handleAddPortfolioItem = () => {
    Alert.alert('Add Investment', 'Portfolio item creation feature coming soon!');
  };

  const renderPortfolioItem = ({ item }: { item: PortfolioItem }) => {
    const totalValue = item.quantity * item.current_price;
    const totalCost = item.quantity * item.purchase_price;
    const profitLoss = totalValue - totalCost;
    const profitLossPercent = (profitLoss / totalCost) * 100;

    return (
      <TouchableOpacity style={styles.portfolioCard}>
        <View style={styles.portfolioHeader}>
          <View style={styles.portfolioInfo}>
            <Ionicons
              name={getPortfolioIcon(item.type)}
              size={24}
              color={getPortfolioColor(item.type)}
            />
            <View style={styles.portfolioDetails}>
              <Text style={styles.portfolioSymbol}>{item.symbol}</Text>
              <Text style={styles.portfolioName}>{item.name}</Text>
              <Text style={styles.portfolioType}>
                {item.type.replace('_', ' ').toUpperCase()} • {item.quantity} shares
              </Text>
            </View>
          </View>
          <View style={styles.valueContainer}>
            <Text style={styles.currentValue}>
              {formatCurrency(totalValue)}
            </Text>
            <Text
              style={[
                styles.profitLoss,
                { color: profitLoss >= 0 ? '#4CAF50' : '#F44336' },
              ]}
            >
              {profitLoss >= 0 ? '+' : ''}
              {formatCurrency(profitLoss)} ({profitLossPercent.toFixed(1)}%)
            </Text>
          </View>
        </View>
        <View style={styles.priceInfo}>
          <View style={styles.priceItem}>
            <Text style={styles.priceLabel}>Purchase Price</Text>
            <Text style={styles.priceValue}>
              {formatCurrency(item.purchase_price)}
            </Text>
          </View>
          <View style={styles.priceItem}>
            <Text style={styles.priceLabel}>Current Price</Text>
            <Text style={styles.priceValue}>
              {formatCurrency(item.current_price)}
            </Text>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  const EmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons name="trending-up-outline" size={64} color="#ccc" />
      <Text style={styles.emptyStateTitle}>No Investments Yet</Text>
      <Text style={styles.emptyStateText}>
        Start building your investment portfolio
      </Text>
      <TouchableOpacity style={styles.addButton} onPress={handleAddPortfolioItem}>
        <Ionicons name="add" size={24} color="white" />
        <Text style={styles.addButtonText}>Add Investment</Text>
      </TouchableOpacity>
    </View>
  );

  const totalPortfolioValue = portfolio.reduce(
    (sum, item) => sum + (item.quantity * item.current_price),
    0
  );

  const totalCost = portfolio.reduce(
    (sum, item) => sum + (item.quantity * item.purchase_price),
    0
  );

  const totalProfitLoss = totalPortfolioValue - totalCost;
  const totalProfitLossPercent = totalCost > 0 ? (totalProfitLoss / totalCost) * 100 : 0;

  return (
    <View style={styles.container}>
      {portfolio.length > 0 && (
        <View style={styles.summaryCard}>
          <Text style={styles.summaryTitle}>Portfolio Summary</Text>
          <Text style={styles.totalValue}>
            {formatCurrency(totalPortfolioValue)}
          </Text>
          <Text
            style={[
              styles.totalProfitLoss,
              { color: totalProfitLoss >= 0 ? '#4CAF50' : '#F44336' },
            ]}
          >
            {totalProfitLoss >= 0 ? '+' : ''}
            {formatCurrency(totalProfitLoss)} ({totalProfitLossPercent.toFixed(1)}%)
          </Text>
        </View>
      )}

      {portfolio.length === 0 ? (
        <EmptyState />
      ) : (
        <>
          <FlatList
            data={portfolio}
            renderItem={renderPortfolioItem}
            keyExtractor={(item) => item.id}
            contentContainerStyle={styles.listContainer}
          />
          <TouchableOpacity style={styles.fab} onPress={handleAddPortfolioItem}>
            <Ionicons name="add" size={24} color="white" />
          </TouchableOpacity>
        </>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  summaryCard: {
    backgroundColor: '#007AFF',
    margin: 16,
    padding: 20,
    borderRadius: 12,
    alignItems: 'center',
  },
  summaryTitle: {
    color: 'white',
    fontSize: 16,
    opacity: 0.9,
  },
  totalValue: {
    color: 'white',
    fontSize: 28,
    fontWeight: 'bold',
    marginTop: 4,
  },
  totalProfitLoss: {
    fontSize: 16,
    marginTop: 4,
  },
  listContainer: {
    padding: 16,
  },
  portfolioCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  portfolioHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  portfolioInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  portfolioDetails: {
    marginLeft: 12,
    flex: 1,
  },
  portfolioSymbol: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  portfolioName: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  portfolioType: {
    fontSize: 12,
    color: '#999',
    marginTop: 2,
  },
  valueContainer: {
    alignItems: 'flex-end',
  },
  currentValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  profitLoss: {
    fontSize: 12,
    marginTop: 2,
  },
  priceInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  priceItem: {
    alignItems: 'center',
  },
  priceLabel: {
    fontSize: 12,
    color: '#666',
  },
  priceValue: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
    marginTop: 2,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  emptyStateTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 16,
  },
  emptyStateText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginTop: 8,
    marginBottom: 32,
  },
  addButton: {
    backgroundColor: '#007AFF',
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  addButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  fab: {
    position: 'absolute',
    bottom: 20,
    right: 20,
    backgroundColor: '#007AFF',
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
});

export default PortfolioScreen;
