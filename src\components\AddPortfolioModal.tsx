import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TextInput,
  TouchableOpacity,
  Alert,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { supabase } from '../../lib/supabase';
import { useAuth } from '../contexts/AuthContext';
import { PortfolioType } from '../types';

interface AddPortfolioModalProps {
  visible: boolean;
  onClose: () => void;
  onPortfolioAdded: () => void;
}

const AddPortfolioModal: React.FC<AddPortfolioModalProps> = ({
  visible,
  onClose,
  onPortfolioAdded,
}) => {
  const { user } = useAuth();
  const [symbol, setSymbol] = useState('');
  const [name, setName] = useState('');
  const [type, setType] = useState<PortfolioType>('stock');
  const [quantity, setQuantity] = useState('');
  const [purchasePrice, setPurchasePrice] = useState('');
  const [currentPrice, setCurrentPrice] = useState('');
  const [purchaseDate, setPurchaseDate] = useState(new Date().toISOString().split('T')[0]);
  const [loading, setLoading] = useState(false);

  const portfolioTypes: { value: PortfolioType; label: string; icon: keyof typeof Ionicons.glyphMap }[] = [
    { value: 'stock', label: 'Stock', icon: 'trending-up' },
    { value: 'crypto', label: 'Cryptocurrency', icon: 'logo-bitcoin' },
    { value: 'bond', label: 'Bond', icon: 'document' },
    { value: 'mutual_fund', label: 'Mutual Fund', icon: 'pie-chart' },
    { value: 'etf', label: 'ETF', icon: 'bar-chart' },
  ];

  const handleSubmit = async () => {
    if (!symbol.trim()) {
      Alert.alert('Error', 'Please enter a symbol');
      return;
    }

    if (!name.trim()) {
      Alert.alert('Error', 'Please enter a name');
      return;
    }

    if (!quantity.trim()) {
      Alert.alert('Error', 'Please enter the quantity');
      return;
    }

    if (!purchasePrice.trim()) {
      Alert.alert('Error', 'Please enter the purchase price');
      return;
    }

    if (!currentPrice.trim()) {
      Alert.alert('Error', 'Please enter the current price');
      return;
    }

    const quantityNumber = parseFloat(quantity);
    if (isNaN(quantityNumber) || quantityNumber <= 0) {
      Alert.alert('Error', 'Please enter a valid quantity');
      return;
    }

    const purchasePriceNumber = parseFloat(purchasePrice);
    if (isNaN(purchasePriceNumber) || purchasePriceNumber <= 0) {
      Alert.alert('Error', 'Please enter a valid purchase price');
      return;
    }

    const currentPriceNumber = parseFloat(currentPrice);
    if (isNaN(currentPriceNumber) || currentPriceNumber <= 0) {
      Alert.alert('Error', 'Please enter a valid current price');
      return;
    }

    if (!user) {
      Alert.alert('Error', 'User not authenticated');
      return;
    }

    setLoading(true);

    try {
      const { error } = await supabase
        .from('portfolio')
        .insert({
          user_id: user.id,
          symbol: symbol.trim().toUpperCase(),
          name: name.trim(),
          type,
          quantity: quantityNumber,
          purchase_price: purchasePriceNumber,
          current_price: currentPriceNumber,
          purchase_date: purchaseDate,
        });

      if (error) {
        throw error;
      }

      Alert.alert('Success', 'Investment added successfully');
      resetForm();
      onPortfolioAdded();
      onClose();
    } catch (error: any) {
      Alert.alert('Error', error.message || 'Failed to add investment');
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setSymbol('');
    setName('');
    setType('stock');
    setQuantity('');
    setPurchasePrice('');
    setCurrentPrice('');
    setPurchaseDate(new Date().toISOString().split('T')[0]);
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={handleClose}
    >
      <KeyboardAvoidingView
        style={styles.container}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <View style={styles.header}>
          <TouchableOpacity onPress={handleClose}>
            <Ionicons name="close" size={24} color="#333" />
          </TouchableOpacity>
          <Text style={styles.title}>Add Investment</Text>
          <TouchableOpacity
            onPress={handleSubmit}
            disabled={loading}
            style={[styles.saveButton, loading && styles.saveButtonDisabled]}
          >
            <Text style={[styles.saveButtonText, loading && styles.saveButtonTextDisabled]}>
              {loading ? 'Saving...' : 'Save'}
            </Text>
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content}>
          <View style={styles.section}>
            <Text style={styles.label}>Symbol *</Text>
            <TextInput
              style={styles.input}
              value={symbol}
              onChangeText={setSymbol}
              placeholder="e.g., AAPL, BTC"
              autoCapitalize="characters"
            />
          </View>

          <View style={styles.section}>
            <Text style={styles.label}>Name *</Text>
            <TextInput
              style={styles.input}
              value={name}
              onChangeText={setName}
              placeholder="e.g., Apple Inc., Bitcoin"
              autoCapitalize="words"
            />
          </View>

          <View style={styles.section}>
            <Text style={styles.label}>Type *</Text>
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
              <View style={styles.typeButtons}>
                {portfolioTypes.map((portfolioType) => (
                  <TouchableOpacity
                    key={portfolioType.value}
                    style={[
                      styles.typeButton,
                      type === portfolioType.value && styles.typeButtonSelected,
                    ]}
                    onPress={() => setType(portfolioType.value)}
                  >
                    <Ionicons
                      name={portfolioType.icon}
                      size={16}
                      color={type === portfolioType.value ? '#007AFF' : '#666'}
                    />
                    <Text
                      style={[
                        styles.typeButtonText,
                        type === portfolioType.value && styles.typeButtonTextSelected,
                      ]}
                    >
                      {portfolioType.label}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </ScrollView>
          </View>

          <View style={styles.section}>
            <Text style={styles.label}>Quantity *</Text>
            <TextInput
              style={styles.input}
              value={quantity}
              onChangeText={setQuantity}
              placeholder="0.00"
              keyboardType="numeric"
            />
          </View>

          <View style={styles.section}>
            <Text style={styles.label}>Purchase Price *</Text>
            <TextInput
              style={styles.input}
              value={purchasePrice}
              onChangeText={setPurchasePrice}
              placeholder="0.00"
              keyboardType="numeric"
            />
          </View>

          <View style={styles.section}>
            <Text style={styles.label}>Current Price *</Text>
            <TextInput
              style={styles.input}
              value={currentPrice}
              onChangeText={setCurrentPrice}
              placeholder="0.00"
              keyboardType="numeric"
            />
          </View>

          <View style={styles.section}>
            <Text style={styles.label}>Purchase Date *</Text>
            <TextInput
              style={styles.input}
              value={purchaseDate}
              onChangeText={setPurchaseDate}
              placeholder="YYYY-MM-DD"
            />
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  saveButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
    backgroundColor: '#007AFF',
  },
  saveButtonDisabled: {
    backgroundColor: '#ccc',
  },
  saveButtonText: {
    color: 'white',
    fontWeight: '600',
  },
  saveButtonTextDisabled: {
    color: '#999',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  section: {
    marginBottom: 24,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  input: {
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
  },
  typeButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  typeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  typeButtonSelected: {
    borderColor: '#007AFF',
    backgroundColor: '#f0f8ff',
  },
  typeButtonText: {
    fontSize: 12,
    color: '#666',
    marginLeft: 4,
  },
  typeButtonTextSelected: {
    color: '#007AFF',
    fontWeight: '600',
  },
});

export default AddPortfolioModal;
