
/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateEventEmitterH.js
 */
#pragma once

#include <react/renderer/components/view/ViewEventEmitter.h>


namespace facebook::react {
class RNDatePickerEventEmitter : public ViewEventEmitter {
 public:
  using ViewEventEmitter::ViewEventEmitter;

  struct OnChange {
      double timestamp;
    };

  struct OnConfirm {
      double timestamp;
    };

  struct OnCancel {
      
    };
  void onChange(OnChange value) const;

  void onConfirm(OnConfirm value) const;

  void onCancel(OnCancel value) const;
};
} // namespace facebook::react
