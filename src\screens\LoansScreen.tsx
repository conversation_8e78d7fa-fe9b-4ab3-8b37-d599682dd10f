import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
  RefreshControl,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useFinancialData } from '../hooks/useFinancialData';
import AddLoanModal from '../components/AddLoanModal';
import { Loan } from '../types';

const LoansScreen: React.FC = () => {
  const { loans, loading, fetchLoans } = useFinancialData();
  const [activeTab, setActiveTab] = useState<'given' | 'taken'>('given');
  const [showAddModal, setShowAddModal] = useState(false);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return '#4CAF50';
      case 'overdue':
        return '#F44336';
      case 'paid':
        return '#666';
      default:
        return '#666';
    }
  };

  const handleAddLoan = () => {
    setShowAddModal(true);
  };

  const handleLoanAdded = () => {
    fetchLoans();
  };

  const filteredLoans = loans.filter(loan => loan.type === activeTab);

  const renderLoan = ({ item }: { item: Loan }) => (
    <TouchableOpacity style={styles.loanCard}>
      <View style={styles.loanHeader}>
        <View style={styles.loanInfo}>
          <Text style={styles.loanTitle}>
            {item.type === 'given' ? item.borrower_name : item.lender_name}
          </Text>
          <Text style={styles.loanDescription}>
            {item.description || `${item.type === 'given' ? 'Loan given' : 'Loan taken'}`}
          </Text>
          {item.due_date && (
            <Text style={styles.loanDueDate}>
              Due: {formatDate(item.due_date)}
            </Text>
          )}
        </View>
        <View style={styles.loanAmount}>
          <Text style={styles.amount}>{formatCurrency(item.amount)}</Text>
          <View style={[styles.statusBadge, { backgroundColor: getStatusColor(item.status) }]}>
            <Text style={styles.statusText}>
              {item.status.charAt(0).toUpperCase() + item.status.slice(1)}
            </Text>
          </View>
        </View>
      </View>
      {item.interest_rate && (
        <View style={styles.interestRate}>
          <Ionicons name="trending-up" size={16} color="#666" />
          <Text style={styles.interestText}>
            {item.interest_rate}% interest rate
          </Text>
        </View>
      )}
    </TouchableOpacity>
  );

  const TabButton = ({ 
    type, 
    label 
  }: { 
    type: 'given' | 'taken'; 
    label: string 
  }) => (
    <TouchableOpacity
      style={[
        styles.tabButton,
        activeTab === type && styles.tabButtonActive,
      ]}
      onPress={() => setActiveTab(type)}
    >
      <Text
        style={[
          styles.tabButtonText,
          activeTab === type && styles.tabButtonTextActive,
        ]}
      >
        {label}
      </Text>
    </TouchableOpacity>
  );

  const EmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons name="cash-outline" size={64} color="#ccc" />
      <Text style={styles.emptyStateTitle}>
        No {activeTab === 'given' ? 'Loans Given' : 'Loans Taken'}
      </Text>
      <Text style={styles.emptyStateText}>
        {activeTab === 'given' 
          ? 'Track money you\'ve lent to others'
          : 'Track money you\'ve borrowed'
        }
      </Text>
      <TouchableOpacity style={styles.addButton} onPress={handleAddLoan}>
        <Ionicons name="add" size={24} color="white" />
        <Text style={styles.addButtonText}>Add Loan</Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <View style={styles.container}>
      <View style={styles.tabContainer}>
        <TabButton type="given" label="Loans Given" />
        <TabButton type="taken" label="Loans Taken" />
      </View>

      {filteredLoans.length === 0 ? (
        <EmptyState />
      ) : (
        <>
          <FlatList
            data={filteredLoans}
            renderItem={renderLoan}
            keyExtractor={(item) => item.id}
            contentContainerStyle={styles.listContainer}
            refreshControl={
              <RefreshControl refreshing={loading} onRefresh={fetchLoans} />
            }
          />
          <TouchableOpacity style={styles.fab} onPress={handleAddLoan}>
            <Ionicons name="add" size={24} color="white" />
          </TouchableOpacity>
        </>
      )}

      <AddLoanModal
        visible={showAddModal}
        onClose={() => setShowAddModal(false)}
        onLoanAdded={handleLoanAdded}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: 'white',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  tabButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderRadius: 8,
    marginHorizontal: 4,
  },
  tabButtonActive: {
    backgroundColor: '#007AFF',
  },
  tabButtonText: {
    fontSize: 16,
    color: '#666',
    fontWeight: '500',
  },
  tabButtonTextActive: {
    color: 'white',
  },
  listContainer: {
    padding: 16,
  },
  loanCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  loanHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  loanInfo: {
    flex: 1,
  },
  loanTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  loanDescription: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  loanDueDate: {
    fontSize: 12,
    color: '#999',
    marginTop: 4,
  },
  loanAmount: {
    alignItems: 'flex-end',
  },
  amount: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginTop: 4,
  },
  statusText: {
    fontSize: 12,
    color: 'white',
    fontWeight: '500',
  },
  interestRate: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  interestText: {
    fontSize: 14,
    color: '#666',
    marginLeft: 4,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  emptyStateTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 16,
  },
  emptyStateText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginTop: 8,
    marginBottom: 32,
  },
  addButton: {
    backgroundColor: '#007AFF',
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  addButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  fab: {
    position: 'absolute',
    bottom: 20,
    right: 20,
    backgroundColor: '#007AFF',
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
});

export default LoansScreen;
