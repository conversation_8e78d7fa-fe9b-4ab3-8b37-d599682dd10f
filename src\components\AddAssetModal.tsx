import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TextInput,
  TouchableOpacity,
  Alert,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { supabase } from '../../lib/supabase';
import { useAuth } from '../contexts/AuthContext';
import { AssetType } from '../types';

interface AddAssetModalProps {
  visible: boolean;
  onClose: () => void;
  onAssetAdded: () => void;
}

const AddAssetModal: React.FC<AddAssetModalProps> = ({
  visible,
  onClose,
  onAssetAdded,
}) => {
  const { user } = useAuth();
  const [name, setName] = useState('');
  const [type, setType] = useState<AssetType>('other');
  const [purchasePrice, setPurchasePrice] = useState('');
  const [currentValue, setCurrentValue] = useState('');
  const [purchaseDate, setPurchaseDate] = useState(new Date().toISOString().split('T')[0]);
  const [description, setDescription] = useState('');
  const [loading, setLoading] = useState(false);

  const assetTypes: { value: AssetType; label: string; icon: keyof typeof Ionicons.glyphMap }[] = [
    { value: 'real_estate', label: 'Real Estate', icon: 'home' },
    { value: 'vehicle', label: 'Vehicle', icon: 'car' },
    { value: 'investment', label: 'Investment', icon: 'trending-up' },
    { value: 'jewelry', label: 'Jewelry', icon: 'diamond' },
    { value: 'electronics', label: 'Electronics', icon: 'phone-portrait' },
    { value: 'other', label: 'Other', icon: 'cube' },
  ];

  const handleSubmit = async () => {
    if (!name.trim()) {
      Alert.alert('Error', 'Please enter an asset name');
      return;
    }

    if (!purchasePrice.trim()) {
      Alert.alert('Error', 'Please enter the purchase price');
      return;
    }

    if (!currentValue.trim()) {
      Alert.alert('Error', 'Please enter the current value');
      return;
    }

    const purchasePriceNumber = parseFloat(purchasePrice);
    if (isNaN(purchasePriceNumber) || purchasePriceNumber < 0) {
      Alert.alert('Error', 'Please enter a valid purchase price');
      return;
    }

    const currentValueNumber = parseFloat(currentValue);
    if (isNaN(currentValueNumber) || currentValueNumber < 0) {
      Alert.alert('Error', 'Please enter a valid current value');
      return;
    }

    if (!user) {
      Alert.alert('Error', 'User not authenticated');
      return;
    }

    setLoading(true);

    try {
      const { error } = await supabase
        .from('assets')
        .insert({
          user_id: user.id,
          name: name.trim(),
          type,
          purchase_price: purchasePriceNumber,
          current_value: currentValueNumber,
          purchase_date: purchaseDate,
          description: description.trim() || null,
        });

      if (error) {
        throw error;
      }

      Alert.alert('Success', 'Asset added successfully');
      resetForm();
      onAssetAdded();
      onClose();
    } catch (error: any) {
      Alert.alert('Error', error.message || 'Failed to add asset');
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setName('');
    setType('other');
    setPurchasePrice('');
    setCurrentValue('');
    setPurchaseDate(new Date().toISOString().split('T')[0]);
    setDescription('');
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={handleClose}
    >
      <KeyboardAvoidingView
        style={styles.container}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <View style={styles.header}>
          <TouchableOpacity onPress={handleClose}>
            <Ionicons name="close" size={24} color="#333" />
          </TouchableOpacity>
          <Text style={styles.title}>Add Asset</Text>
          <TouchableOpacity
            onPress={handleSubmit}
            disabled={loading}
            style={[styles.saveButton, loading && styles.saveButtonDisabled]}
          >
            <Text style={[styles.saveButtonText, loading && styles.saveButtonTextDisabled]}>
              {loading ? 'Saving...' : 'Save'}
            </Text>
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content}>
          <View style={styles.section}>
            <Text style={styles.label}>Asset Name *</Text>
            <TextInput
              style={styles.input}
              value={name}
              onChangeText={setName}
              placeholder="e.g., iPhone 15, Honda Civic"
              autoCapitalize="words"
            />
          </View>

          <View style={styles.section}>
            <Text style={styles.label}>Asset Type *</Text>
            <View style={styles.typeGrid}>
              {assetTypes.map((assetType) => (
                <TouchableOpacity
                  key={assetType.value}
                  style={[
                    styles.typeButton,
                    type === assetType.value && styles.typeButtonSelected,
                  ]}
                  onPress={() => setType(assetType.value)}
                >
                  <Ionicons
                    name={assetType.icon}
                    size={20}
                    color={type === assetType.value ? '#007AFF' : '#666'}
                  />
                  <Text
                    style={[
                      styles.typeButtonText,
                      type === assetType.value && styles.typeButtonTextSelected,
                    ]}
                  >
                    {assetType.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          <View style={styles.section}>
            <Text style={styles.label}>Purchase Price *</Text>
            <TextInput
              style={styles.input}
              value={purchasePrice}
              onChangeText={setPurchasePrice}
              placeholder="0.00"
              keyboardType="numeric"
            />
          </View>

          <View style={styles.section}>
            <Text style={styles.label}>Current Value *</Text>
            <TextInput
              style={styles.input}
              value={currentValue}
              onChangeText={setCurrentValue}
              placeholder="0.00"
              keyboardType="numeric"
            />
          </View>

          <View style={styles.section}>
            <Text style={styles.label}>Purchase Date *</Text>
            <TextInput
              style={styles.input}
              value={purchaseDate}
              onChangeText={setPurchaseDate}
              placeholder="YYYY-MM-DD"
            />
          </View>

          <View style={styles.section}>
            <Text style={styles.label}>Description</Text>
            <TextInput
              style={styles.input}
              value={description}
              onChangeText={setDescription}
              placeholder="Optional description"
              multiline
              numberOfLines={3}
            />
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  saveButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
    backgroundColor: '#007AFF',
  },
  saveButtonDisabled: {
    backgroundColor: '#ccc',
  },
  saveButtonText: {
    color: 'white',
    fontWeight: '600',
  },
  saveButtonTextDisabled: {
    color: '#999',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  section: {
    marginBottom: 24,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  input: {
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
  },
  typeGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  typeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#ddd',
    minWidth: '30%',
  },
  typeButtonSelected: {
    borderColor: '#007AFF',
    backgroundColor: '#f0f8ff',
  },
  typeButtonText: {
    fontSize: 14,
    color: '#666',
    marginLeft: 6,
  },
  typeButtonTextSelected: {
    color: '#007AFF',
    fontWeight: '600',
  },
});

export default AddAssetModal;
