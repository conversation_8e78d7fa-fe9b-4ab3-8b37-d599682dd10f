import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

const ReportsScreen: React.FC = () => {
  const [selectedPeriod, setSelectedPeriod] = useState<'week' | 'month' | 'year'>('month');

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const handleExportReport = () => {
    Alert.alert('Export Report', 'Report export feature coming soon!');
  };

  const PeriodButton = ({ 
    period, 
    label 
  }: { 
    period: 'week' | 'month' | 'year'; 
    label: string 
  }) => (
    <TouchableOpacity
      style={[
        styles.periodButton,
        selectedPeriod === period && styles.periodButtonActive,
      ]}
      onPress={() => setSelectedPeriod(period)}
    >
      <Text
        style={[
          styles.periodButtonText,
          selectedPeriod === period && styles.periodButtonTextActive,
        ]}
      >
        {label}
      </Text>
    </TouchableOpacity>
  );

  const ReportCard = ({ 
    title, 
    value, 
    icon, 
    color 
  }: { 
    title: string; 
    value: string; 
    icon: keyof typeof Ionicons.glyphMap; 
    color: string 
  }) => (
    <View style={styles.reportCard}>
      <View style={styles.reportCardHeader}>
        <Ionicons name={icon} size={24} color={color} />
        <Text style={styles.reportCardTitle}>{title}</Text>
      </View>
      <Text style={[styles.reportCardValue, { color }]}>{value}</Text>
    </View>
  );

  const CategoryItem = ({ 
    name, 
    amount, 
    percentage 
  }: { 
    name: string; 
    amount: number; 
    percentage: number 
  }) => (
    <View style={styles.categoryItem}>
      <View style={styles.categoryInfo}>
        <Text style={styles.categoryName}>{name}</Text>
        <Text style={styles.categoryAmount}>{formatCurrency(amount)}</Text>
      </View>
      <View style={styles.categoryBar}>
        <View 
          style={[
            styles.categoryBarFill, 
            { width: `${percentage}%` }
          ]} 
        />
      </View>
      <Text style={styles.categoryPercentage}>{percentage.toFixed(1)}%</Text>
    </View>
  );

  // Mock data - replace with actual data from your state management
  const mockData = {
    totalIncome: 5000,
    totalExpenses: 3500,
    netSavings: 1500,
    categories: [
      { name: 'Food & Dining', amount: 800, percentage: 22.9 },
      { name: 'Transportation', amount: 600, percentage: 17.1 },
      { name: 'Shopping', amount: 500, percentage: 14.3 },
      { name: 'Entertainment', amount: 400, percentage: 11.4 },
      { name: 'Utilities', amount: 350, percentage: 10.0 },
      { name: 'Healthcare', amount: 300, percentage: 8.6 },
      { name: 'Other', amount: 550, percentage: 15.7 },
    ],
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Financial Reports</Text>
        <TouchableOpacity style={styles.exportButton} onPress={handleExportReport}>
          <Ionicons name="download" size={20} color="#007AFF" />
          <Text style={styles.exportButtonText}>Export</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.periodSelector}>
        <PeriodButton period="week" label="This Week" />
        <PeriodButton period="month" label="This Month" />
        <PeriodButton period="year" label="This Year" />
      </View>

      <View style={styles.summaryCards}>
        <ReportCard
          title="Total Income"
          value={formatCurrency(mockData.totalIncome)}
          icon="trending-up"
          color="#4CAF50"
        />
        <ReportCard
          title="Total Expenses"
          value={formatCurrency(mockData.totalExpenses)}
          icon="trending-down"
          color="#F44336"
        />
        <ReportCard
          title="Net Savings"
          value={formatCurrency(mockData.netSavings)}
          icon="wallet"
          color="#2196F3"
        />
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Expense Categories</Text>
        <View style={styles.categoriesContainer}>
          {mockData.categories.map((category, index) => (
            <CategoryItem
              key={index}
              name={category.name}
              amount={category.amount}
              percentage={category.percentage}
            />
          ))}
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Insights</Text>
        <View style={styles.insightsContainer}>
          <View style={styles.insightCard}>
            <Ionicons name="bulb" size={24} color="#FF9800" />
            <View style={styles.insightContent}>
              <Text style={styles.insightTitle}>Spending Trend</Text>
              <Text style={styles.insightText}>
                Your expenses increased by 12% compared to last month
              </Text>
            </View>
          </View>
          
          <View style={styles.insightCard}>
            <Ionicons name="warning" size={24} color="#F44336" />
            <View style={styles.insightContent}>
              <Text style={styles.insightTitle}>Budget Alert</Text>
              <Text style={styles.insightText}>
                You've exceeded your food budget by $150 this month
              </Text>
            </View>
          </View>
          
          <View style={styles.insightCard}>
            <Ionicons name="checkmark-circle" size={24} color="#4CAF50" />
            <View style={styles.insightContent}>
              <Text style={styles.insightTitle}>Savings Goal</Text>
              <Text style={styles.insightText}>
                Great job! You're on track to meet your monthly savings goal
              </Text>
            </View>
          </View>
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Quick Actions</Text>
        <View style={styles.actionsContainer}>
          <TouchableOpacity style={styles.actionButton}>
            <Ionicons name="calendar" size={24} color="#007AFF" />
            <Text style={styles.actionText}>Custom Date Range</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.actionButton}>
            <Ionicons name="pie-chart" size={24} color="#007AFF" />
            <Text style={styles.actionText}>Detailed Charts</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.actionButton}>
            <Ionicons name="settings" size={24} color="#007AFF" />
            <Text style={styles.actionText}>Report Settings</Text>
          </TouchableOpacity>
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    backgroundColor: 'white',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
  },
  exportButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#007AFF',
  },
  exportButtonText: {
    color: '#007AFF',
    marginLeft: 4,
    fontSize: 14,
  },
  periodSelector: {
    flexDirection: 'row',
    backgroundColor: 'white',
    paddingHorizontal: 20,
    paddingBottom: 16,
    gap: 8,
  },
  periodButton: {
    flex: 1,
    paddingVertical: 8,
    alignItems: 'center',
    borderRadius: 6,
    backgroundColor: '#f8f9fa',
  },
  periodButtonActive: {
    backgroundColor: '#007AFF',
  },
  periodButtonText: {
    fontSize: 14,
    color: '#666',
  },
  periodButtonTextActive: {
    color: 'white',
  },
  summaryCards: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 16,
    gap: 10,
  },
  reportCard: {
    flex: 1,
    backgroundColor: 'white',
    padding: 16,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  reportCardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  reportCardTitle: {
    fontSize: 12,
    color: '#666',
    marginLeft: 4,
  },
  reportCardValue: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  section: {
    backgroundColor: 'white',
    margin: 20,
    borderRadius: 8,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
  },
  categoriesContainer: {
    gap: 12,
  },
  categoryItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  categoryInfo: {
    flex: 1,
  },
  categoryName: {
    fontSize: 14,
    color: '#333',
  },
  categoryAmount: {
    fontSize: 12,
    color: '#666',
    marginTop: 2,
  },
  categoryBar: {
    flex: 2,
    height: 6,
    backgroundColor: '#f0f0f0',
    borderRadius: 3,
  },
  categoryBarFill: {
    height: '100%',
    backgroundColor: '#007AFF',
    borderRadius: 3,
  },
  categoryPercentage: {
    fontSize: 12,
    color: '#666',
    width: 40,
    textAlign: 'right',
  },
  insightsContainer: {
    gap: 12,
  },
  insightCard: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    padding: 12,
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
  },
  insightContent: {
    flex: 1,
    marginLeft: 12,
  },
  insightTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
  },
  insightText: {
    fontSize: 12,
    color: '#666',
    marginTop: 2,
  },
  actionsContainer: {
    flexDirection: 'row',
    gap: 12,
  },
  actionButton: {
    flex: 1,
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
  },
  actionText: {
    fontSize: 12,
    color: '#007AFF',
    marginTop: 4,
    textAlign: 'center',
  },
});

export default ReportsScreen;
