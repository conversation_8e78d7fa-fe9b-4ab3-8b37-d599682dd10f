import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Debt } from '../types';

const DebtsScreen: React.FC = () => {
  const [debts, setDebts] = useState<Debt[]>([]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return '#FF9800';
      case 'partial':
        return '#2196F3';
      case 'paid':
        return '#4CAF50';
      default:
        return '#666';
    }
  };

  const handleAddDebt = () => {
    Alert.alert('Add Debt', 'Debt tracking feature coming soon!');
  };

  const renderDebt = ({ item }: { item: Debt }) => {
    const remainingAmount = item.amount - item.amount_paid;
    const paymentProgress = (item.amount_paid / item.amount) * 100;

    return (
      <TouchableOpacity style={styles.debtCard}>
        <View style={styles.debtHeader}>
          <View style={styles.debtInfo}>
            <Text style={styles.debtorName}>{item.debtor_name}</Text>
            {item.debtor_contact && (
              <Text style={styles.debtorContact}>{item.debtor_contact}</Text>
            )}
            <Text style={styles.debtDescription}>
              {item.description || 'Money owed'}
            </Text>
            {item.due_date && (
              <Text style={styles.dueDate}>
                Due: {formatDate(item.due_date)}
              </Text>
            )}
          </View>
          <View style={styles.amountContainer}>
            <Text style={styles.totalAmount}>
              {formatCurrency(item.amount)}
            </Text>
            <View style={[styles.statusBadge, { backgroundColor: getStatusColor(item.status) }]}>
              <Text style={styles.statusText}>
                {item.status.charAt(0).toUpperCase() + item.status.slice(1)}
              </Text>
            </View>
          </View>
        </View>
        
        {item.amount_paid > 0 && (
          <View style={styles.paymentProgress}>
            <View style={styles.progressHeader}>
              <Text style={styles.progressLabel}>Payment Progress</Text>
              <Text style={styles.progressPercent}>
                {paymentProgress.toFixed(0)}%
              </Text>
            </View>
            <View style={styles.progressBar}>
              <View 
                style={[
                  styles.progressFill, 
                  { width: `${paymentProgress}%` }
                ]} 
              />
            </View>
            <View style={styles.progressAmounts}>
              <Text style={styles.paidAmount}>
                Paid: {formatCurrency(item.amount_paid)}
              </Text>
              <Text style={styles.remainingAmount}>
                Remaining: {formatCurrency(remainingAmount)}
              </Text>
            </View>
          </View>
        )}
      </TouchableOpacity>
    );
  };

  const EmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons name="people-outline" size={64} color="#ccc" />
      <Text style={styles.emptyStateTitle}>No Debts Tracked</Text>
      <Text style={styles.emptyStateText}>
        Keep track of money others owe you
      </Text>
      <TouchableOpacity style={styles.addButton} onPress={handleAddDebt}>
        <Ionicons name="add" size={24} color="white" />
        <Text style={styles.addButtonText}>Add Debt</Text>
      </TouchableOpacity>
    </View>
  );

  const totalOwed = debts.reduce((sum, debt) => sum + debt.amount, 0);
  const totalPaid = debts.reduce((sum, debt) => sum + debt.amount_paid, 0);
  const totalRemaining = totalOwed - totalPaid;

  return (
    <View style={styles.container}>
      {debts.length > 0 && (
        <View style={styles.summaryCard}>
          <Text style={styles.summaryTitle}>Debt Summary</Text>
          <View style={styles.summaryRow}>
            <View style={styles.summaryItem}>
              <Text style={styles.summaryLabel}>Total Owed</Text>
              <Text style={styles.summaryValue}>
                {formatCurrency(totalOwed)}
              </Text>
            </View>
            <View style={styles.summaryItem}>
              <Text style={styles.summaryLabel}>Paid</Text>
              <Text style={[styles.summaryValue, { color: '#4CAF50' }]}>
                {formatCurrency(totalPaid)}
              </Text>
            </View>
            <View style={styles.summaryItem}>
              <Text style={styles.summaryLabel}>Remaining</Text>
              <Text style={[styles.summaryValue, { color: '#FF9800' }]}>
                {formatCurrency(totalRemaining)}
              </Text>
            </View>
          </View>
        </View>
      )}

      {debts.length === 0 ? (
        <EmptyState />
      ) : (
        <>
          <FlatList
            data={debts}
            renderItem={renderDebt}
            keyExtractor={(item) => item.id}
            contentContainerStyle={styles.listContainer}
          />
          <TouchableOpacity style={styles.fab} onPress={handleAddDebt}>
            <Ionicons name="add" size={24} color="white" />
          </TouchableOpacity>
        </>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  summaryCard: {
    backgroundColor: 'white',
    margin: 16,
    padding: 20,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
    textAlign: 'center',
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  summaryItem: {
    alignItems: 'center',
  },
  summaryLabel: {
    fontSize: 14,
    color: '#666',
  },
  summaryValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 4,
  },
  listContainer: {
    padding: 16,
  },
  debtCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  debtHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  debtInfo: {
    flex: 1,
  },
  debtorName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  debtorContact: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  debtDescription: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  dueDate: {
    fontSize: 12,
    color: '#999',
    marginTop: 4,
  },
  amountContainer: {
    alignItems: 'flex-end',
  },
  totalAmount: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginTop: 4,
  },
  statusText: {
    fontSize: 12,
    color: 'white',
    fontWeight: '500',
  },
  paymentProgress: {
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  progressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  progressLabel: {
    fontSize: 14,
    color: '#666',
  },
  progressPercent: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
  },
  progressBar: {
    height: 6,
    backgroundColor: '#f0f0f0',
    borderRadius: 3,
    marginBottom: 8,
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#4CAF50',
    borderRadius: 3,
  },
  progressAmounts: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  paidAmount: {
    fontSize: 12,
    color: '#4CAF50',
  },
  remainingAmount: {
    fontSize: 12,
    color: '#FF9800',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  emptyStateTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 16,
  },
  emptyStateText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginTop: 8,
    marginBottom: 32,
  },
  addButton: {
    backgroundColor: '#007AFF',
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  addButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  fab: {
    position: 'absolute',
    bottom: 20,
    right: 20,
    backgroundColor: '#007AFF',
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
});

export default DebtsScreen;
