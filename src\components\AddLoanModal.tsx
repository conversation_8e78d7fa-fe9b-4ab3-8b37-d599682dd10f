import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TextInput,
  TouchableOpacity,
  Alert,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { supabase } from '../../lib/supabase';
import { useAuth } from '../contexts/AuthContext';
import { LoanType } from '../types';

interface AddLoanModalProps {
  visible: boolean;
  onClose: () => void;
  onLoanAdded: () => void;
}

const AddLoanModal: React.FC<AddLoanModalProps> = ({
  visible,
  onClose,
  onLoanAdded,
}) => {
  const { user } = useAuth();
  const [type, setType] = useState<LoanType>('given');
  const [amount, setAmount] = useState('');
  const [interestRate, setInterestRate] = useState('');
  const [borrowerName, setBorrowerName] = useState('');
  const [lenderName, setLenderName] = useState('');
  const [borrowerContact, setBorrowerContact] = useState('');
  const [lenderContact, setLenderContact] = useState('');
  const [description, setDescription] = useState('');
  const [dueDate, setDueDate] = useState('');
  const [loading, setLoading] = useState(false);

  const handleSubmit = async () => {
    if (!amount.trim()) {
      Alert.alert('Error', 'Please enter an amount');
      return;
    }

    if (type === 'given' && !borrowerName.trim()) {
      Alert.alert('Error', 'Please enter the borrower\'s name');
      return;
    }

    if (type === 'taken' && !lenderName.trim()) {
      Alert.alert('Error', 'Please enter the lender\'s name');
      return;
    }

    const amountNumber = parseFloat(amount);
    if (isNaN(amountNumber) || amountNumber <= 0) {
      Alert.alert('Error', 'Please enter a valid amount');
      return;
    }

    const interestRateNumber = interestRate ? parseFloat(interestRate) : 0;
    if (interestRate && (isNaN(interestRateNumber) || interestRateNumber < 0)) {
      Alert.alert('Error', 'Please enter a valid interest rate');
      return;
    }

    if (!user) {
      Alert.alert('Error', 'User not authenticated');
      return;
    }

    setLoading(true);

    try {
      const { error } = await supabase
        .from('loans')
        .insert({
          user_id: user.id,
          type,
          amount: amountNumber,
          interest_rate: interestRateNumber,
          borrower_name: type === 'given' ? borrowerName.trim() : null,
          lender_name: type === 'taken' ? lenderName.trim() : null,
          borrower_contact: type === 'given' ? borrowerContact.trim() || null : null,
          lender_contact: type === 'taken' ? lenderContact.trim() || null : null,
          description: description.trim() || null,
          due_date: dueDate || null,
          status: 'active',
        });

      if (error) {
        throw error;
      }

      Alert.alert('Success', 'Loan added successfully');
      resetForm();
      onLoanAdded();
      onClose();
    } catch (error: any) {
      Alert.alert('Error', error.message || 'Failed to add loan');
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setType('given');
    setAmount('');
    setInterestRate('');
    setBorrowerName('');
    setLenderName('');
    setBorrowerContact('');
    setLenderContact('');
    setDescription('');
    setDueDate('');
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={handleClose}
    >
      <KeyboardAvoidingView
        style={styles.container}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <View style={styles.header}>
          <TouchableOpacity onPress={handleClose}>
            <Ionicons name="close" size={24} color="#333" />
          </TouchableOpacity>
          <Text style={styles.title}>Add Loan</Text>
          <TouchableOpacity
            onPress={handleSubmit}
            disabled={loading}
            style={[styles.saveButton, loading && styles.saveButtonDisabled]}
          >
            <Text style={[styles.saveButtonText, loading && styles.saveButtonTextDisabled]}>
              {loading ? 'Saving...' : 'Save'}
            </Text>
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content}>
          <View style={styles.section}>
            <Text style={styles.label}>Loan Type *</Text>
            <View style={styles.typeButtons}>
              <TouchableOpacity
                style={[
                  styles.typeButton,
                  type === 'given' && styles.typeButtonSelected,
                ]}
                onPress={() => setType('given')}
              >
                <Ionicons
                  name="arrow-up"
                  size={20}
                  color={type === 'given' ? '#4CAF50' : '#666'}
                />
                <Text
                  style={[
                    styles.typeButtonText,
                    type === 'given' && { color: '#4CAF50' },
                  ]}
                >
                  Loan Given
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.typeButton,
                  type === 'taken' && styles.typeButtonSelected,
                ]}
                onPress={() => setType('taken')}
              >
                <Ionicons
                  name="arrow-down"
                  size={20}
                  color={type === 'taken' ? '#F44336' : '#666'}
                />
                <Text
                  style={[
                    styles.typeButtonText,
                    type === 'taken' && { color: '#F44336' },
                  ]}
                >
                  Loan Taken
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          <View style={styles.section}>
            <Text style={styles.label}>Amount *</Text>
            <TextInput
              style={styles.input}
              value={amount}
              onChangeText={setAmount}
              placeholder="0.00"
              keyboardType="numeric"
            />
          </View>

          {type === 'given' ? (
            <>
              <View style={styles.section}>
                <Text style={styles.label}>Borrower Name *</Text>
                <TextInput
                  style={styles.input}
                  value={borrowerName}
                  onChangeText={setBorrowerName}
                  placeholder="Enter borrower's name"
                  autoCapitalize="words"
                />
              </View>
              <View style={styles.section}>
                <Text style={styles.label}>Borrower Contact</Text>
                <TextInput
                  style={styles.input}
                  value={borrowerContact}
                  onChangeText={setBorrowerContact}
                  placeholder="Phone or email (optional)"
                />
              </View>
            </>
          ) : (
            <>
              <View style={styles.section}>
                <Text style={styles.label}>Lender Name *</Text>
                <TextInput
                  style={styles.input}
                  value={lenderName}
                  onChangeText={setLenderName}
                  placeholder="Enter lender's name"
                  autoCapitalize="words"
                />
              </View>
              <View style={styles.section}>
                <Text style={styles.label}>Lender Contact</Text>
                <TextInput
                  style={styles.input}
                  value={lenderContact}
                  onChangeText={setLenderContact}
                  placeholder="Phone or email (optional)"
                />
              </View>
            </>
          )}

          <View style={styles.section}>
            <Text style={styles.label}>Interest Rate (%)</Text>
            <TextInput
              style={styles.input}
              value={interestRate}
              onChangeText={setInterestRate}
              placeholder="0.00"
              keyboardType="numeric"
            />
          </View>

          <View style={styles.section}>
            <Text style={styles.label}>Due Date</Text>
            <TextInput
              style={styles.input}
              value={dueDate}
              onChangeText={setDueDate}
              placeholder="YYYY-MM-DD (optional)"
            />
          </View>

          <View style={styles.section}>
            <Text style={styles.label}>Description</Text>
            <TextInput
              style={styles.input}
              value={description}
              onChangeText={setDescription}
              placeholder="Optional description"
              multiline
              numberOfLines={3}
            />
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  saveButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
    backgroundColor: '#007AFF',
  },
  saveButtonDisabled: {
    backgroundColor: '#ccc',
  },
  saveButtonText: {
    color: 'white',
    fontWeight: '600',
  },
  saveButtonTextDisabled: {
    color: '#999',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  section: {
    marginBottom: 24,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  input: {
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
  },
  typeButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  typeButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'white',
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  typeButtonSelected: {
    borderWidth: 2,
  },
  typeButtonText: {
    fontSize: 16,
    color: '#666',
    marginLeft: 8,
    fontWeight: '600',
  },
});

export default AddLoanModal;
