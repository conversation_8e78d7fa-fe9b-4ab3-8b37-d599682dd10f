import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TextInput,
  TouchableOpacity,
  Alert,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { supabase } from '../../lib/supabase';
import { useAuth } from '../contexts/AuthContext';
import { AccountType } from '../types';

interface AddAccountModalProps {
  visible: boolean;
  onClose: () => void;
  onAccountAdded: () => void;
}

const AddAccountModal: React.FC<AddAccountModalProps> = ({
  visible,
  onClose,
  onAccountAdded,
}) => {
  const { user } = useAuth();
  const [name, setName] = useState('');
  const [type, setType] = useState<AccountType>('checking');
  const [balance, setBalance] = useState('');
  const [currency, setCurrency] = useState('USD');
  const [loading, setLoading] = useState(false);

  const accountTypes: { value: AccountType; label: string; icon: keyof typeof Ionicons.glyphMap }[] = [
    { value: 'checking', label: 'Checking Account', icon: 'card' },
    { value: 'savings', label: 'Savings Account', icon: 'wallet' },
    { value: 'credit', label: 'Credit Card', icon: 'card-outline' },
    { value: 'investment', label: 'Investment Account', icon: 'trending-up' },
    { value: 'cash', label: 'Cash', icon: 'cash' },
  ];

  const handleSubmit = async () => {
    if (!name.trim()) {
      Alert.alert('Error', 'Please enter an account name');
      return;
    }

    if (!balance.trim()) {
      Alert.alert('Error', 'Please enter an initial balance');
      return;
    }

    const balanceNumber = parseFloat(balance);
    if (isNaN(balanceNumber)) {
      Alert.alert('Error', 'Please enter a valid balance amount');
      return;
    }

    if (!user) {
      Alert.alert('Error', 'User not authenticated');
      return;
    }

    setLoading(true);

    try {
      const { error } = await supabase
        .from('accounts')
        .insert({
          user_id: user.id,
          name: name.trim(),
          type,
          balance: balanceNumber,
          currency,
        });

      if (error) {
        throw error;
      }

      Alert.alert('Success', 'Account added successfully');
      resetForm();
      onAccountAdded();
      onClose();
    } catch (error: any) {
      Alert.alert('Error', error.message || 'Failed to add account');
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setName('');
    setType('checking');
    setBalance('');
    setCurrency('USD');
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={handleClose}
    >
      <KeyboardAvoidingView
        style={styles.container}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <View style={styles.header}>
          <TouchableOpacity onPress={handleClose}>
            <Ionicons name="close" size={24} color="#333" />
          </TouchableOpacity>
          <Text style={styles.title}>Add Account</Text>
          <TouchableOpacity
            onPress={handleSubmit}
            disabled={loading}
            style={[styles.saveButton, loading && styles.saveButtonDisabled]}
          >
            <Text style={[styles.saveButtonText, loading && styles.saveButtonTextDisabled]}>
              {loading ? 'Saving...' : 'Save'}
            </Text>
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content}>
          <View style={styles.section}>
            <Text style={styles.label}>Account Name *</Text>
            <TextInput
              style={styles.input}
              value={name}
              onChangeText={setName}
              placeholder="e.g., Main Checking"
              autoCapitalize="words"
            />
          </View>

          <View style={styles.section}>
            <Text style={styles.label}>Account Type *</Text>
            <View style={styles.typeGrid}>
              {accountTypes.map((accountType) => (
                <TouchableOpacity
                  key={accountType.value}
                  style={[
                    styles.typeButton,
                    type === accountType.value && styles.typeButtonSelected,
                  ]}
                  onPress={() => setType(accountType.value)}
                >
                  <Ionicons
                    name={accountType.icon}
                    size={24}
                    color={type === accountType.value ? '#007AFF' : '#666'}
                  />
                  <Text
                    style={[
                      styles.typeButtonText,
                      type === accountType.value && styles.typeButtonTextSelected,
                    ]}
                  >
                    {accountType.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          <View style={styles.section}>
            <Text style={styles.label}>Initial Balance *</Text>
            <TextInput
              style={styles.input}
              value={balance}
              onChangeText={setBalance}
              placeholder="0.00"
              keyboardType="numeric"
            />
          </View>

          <View style={styles.section}>
            <Text style={styles.label}>Currency</Text>
            <TextInput
              style={styles.input}
              value={currency}
              onChangeText={setCurrency}
              placeholder="USD"
              autoCapitalize="characters"
              maxLength={3}
            />
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  saveButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
    backgroundColor: '#007AFF',
  },
  saveButtonDisabled: {
    backgroundColor: '#ccc',
  },
  saveButtonText: {
    color: 'white',
    fontWeight: '600',
  },
  saveButtonTextDisabled: {
    color: '#999',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  section: {
    marginBottom: 24,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  input: {
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
  },
  typeGrid: {
    gap: 12,
  },
  typeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  typeButtonSelected: {
    borderColor: '#007AFF',
    backgroundColor: '#f0f8ff',
  },
  typeButtonText: {
    fontSize: 16,
    color: '#666',
    marginLeft: 12,
  },
  typeButtonTextSelected: {
    color: '#007AFF',
    fontWeight: '600',
  },
});

export default AddAccountModal;
